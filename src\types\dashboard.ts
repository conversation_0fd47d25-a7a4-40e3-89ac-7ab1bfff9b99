export interface CardPosition {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  visible: boolean;
}

export interface DashboardLayout {
  cards: Record<string, CardPosition>;
  gridLayout: string[];
}

export interface CardConfig {
  id: string;
  title: string;
  description: string;
  defaultVisible: boolean;
  category: 'field' | 'weather' | 'analytics' | 'recommendations' | 'controls';
}

export const CARD_CONFIGS: Record<string, CardConfig> = {
  'left-field': {
    id: 'left-field',
    title: 'Corn Field',
    description: 'Left field information and controls',
    defaultVisible: true,
    category: 'field'
  },
  'weather': {
    id: 'weather',
    title: 'Weather',
    description: 'Current weather conditions',
    defaultVisible: true,
    category: 'weather'
  },
  'rate': {
    id: 'rate',
    title: 'Rate Card',
    description: 'Application rates and zones',
    defaultVisible: true,
    category: 'controls'
  },
  'right-field': {
    id: 'right-field',
    title: 'Empty Field',
    description: 'Right field information and controls',
    defaultVisible: true,
    category: 'field'
  },
  'status-cards': {
    id: 'status-cards',
    title: 'Field Status',
    description: 'Plant health, water depth, and soil conditions',
    defaultVisible: true,
    category: 'analytics'
  },
  'recommendations': {
    id: 'recommendations',
    title: 'Recommendations',
    description: 'AI-powered farming recommendations',
    defaultVisible: true,
    category: 'recommendations'
  },
  'map': {
    id: 'map',
    title: 'Field Map',
    description: 'Interactive field mapping',
    defaultVisible: true,
    category: 'analytics'
  },
  'crop-selector': {
    id: 'crop-selector',
    title: 'Crop Controls',
    description: 'Crop selection and rate settings',
    defaultVisible: true,
    category: 'controls'
  },
  'growth-chart': {
    id: 'growth-chart',
    title: 'Growth Rate',
    description: 'Growth rate analytics and trends',
    defaultVisible: true,
    category: 'analytics'
  }
};
