'use client';

import React, { useState } from 'react';
import Image from "next/image";
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  rectSortingStrategy,
} from '@dnd-kit/sortable';

import { DraggableCard } from '../components/DraggableCard';
import { DashboardSettings } from '../components/DashboardSettings';
import { FieldCard } from '../components/cards/FieldCard';
import { WeatherCard } from '../components/cards/WeatherCard';
import { useDashboardLayout } from '../hooks/useDashboardLayout';

const mapPlaceholder = "https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=200&q=80";

export default function Home() {
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const { layout, reorderCards, getVisibleCards, isLoading } = useDashboardLayout();

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const visibleCards = getVisibleCards();
      const oldIndex = visibleCards.indexOf(active.id as string);
      const newIndex = visibleCards.indexOf(over?.id as string);

      const newOrder = arrayMove(visibleCards, oldIndex, newIndex);
      reorderCards(newOrder);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-200 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }
  const visibleCards = getVisibleCards();

  const renderCard = (cardId: string) => {
    switch (cardId) {
      case 'left-field':
        return (
          <DraggableCard id={cardId} className="col-span-2">
            <FieldCard
              title="Corn field"
              area="18 ha"
              imageUrl="https://images.unsplash.com/photo-1501785888041-af3ef285b470?auto=format&fit=crop&w=400&q=80"
              alt="Corn field"
            />
          </DraggableCard>
        );
      case 'weather':
        return (
          <DraggableCard id={cardId} className="col-span-5">
            <WeatherCard />
          </DraggableCard>
        );
      case 'rate':
        return (
          <DraggableCard id={cardId} className="col-span-3">
            <div className="bg-white rounded-2xl shadow-lg p-3 flex flex-col gap-2 border border-gray-100 h-full">
              <div className="flex flex-col gap-2 mb-2">
                <span className="text-xs text-gray-500">Standard rate</span>
                <div className="flex items-center justify-between">
                  <input className="border rounded px-2 py-1 w-16 text-sm text-center font-medium" defaultValue="100" readOnly />
                  <div className="flex bg-gray-200 rounded-full p-0.5 gap-0.5 text-xs">
                    <button className="bg-lime-300 text-green-900 rounded-full px-2 py-0.5 font-semibold shadow">kg/ha</button>
                    <button className="text-gray-500 rounded-full px-2 py-0.5 font-semibold">L/ha</button>
                  </div>
                </div>
              </div>
              <div className="flex justify-between items-center text-xs text-gray-500 mb-1">
                <span>Rate for zone</span>
                <button className="text-lime-600 hover:text-lime-700 font-medium text-xs">Edit</button>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div className="flex flex-col items-start bg-gray-50 rounded p-2">
                  <div className="flex items-center gap-1 mb-1">
                    <span className="w-2 h-2 rounded-full bg-purple-500 inline-block"></span>
                    <span className="font-semibold text-gray-700 text-xs">Very high</span>
                  </div>
                  <div className="flex justify-between w-full text-xs">
                    <span className="font-bold text-gray-800">70 kg/h</span>
                    <span className="text-gray-500">1,8 ha</span>
                  </div>
                </div>
                <div className="flex flex-col items-start bg-gray-50 rounded p-2">
                  <div className="flex items-center gap-1 mb-1">
                    <span className="w-2 h-2 rounded-full bg-purple-500 inline-block"></span>
                    <span className="font-semibold text-gray-700 text-xs">High</span>
                  </div>
                  <div className="flex justify-between w-full text-xs">
                    <span className="font-bold text-gray-800">85 kg/h</span>
                    <span className="text-gray-500">4,5 ha</span>
                  </div>
                </div>
                <div className="flex flex-col items-start bg-gray-50 rounded p-2">
                  <div className="flex items-center gap-1 mb-1">
                    <span className="w-2 h-2 rounded-full bg-blue-500 inline-block"></span>
                    <span className="font-semibold text-gray-700 text-xs">Average</span>
                  </div>
                  <div className="flex justify-between w-full text-xs">
                    <span className="font-bold text-gray-800">100 kg/h</span>
                    <span className="text-gray-500">5,5 ha</span>
                  </div>
                </div>
                <div className="flex flex-col items-start bg-gray-50 rounded p-2">
                  <div className="flex items-center gap-1 mb-1">
                    <span className="w-2 h-2 rounded-full bg-gray-400 inline-block"></span>
                    <span className="font-semibold text-gray-700 text-xs">Low</span>
                  </div>
                  <div className="flex justify-between w-full text-xs">
                    <span className="font-bold text-gray-800">115 kg/h</span>
                    <span className="text-gray-500">6,2 ha</span>
                  </div>
                </div>
              </div>
            </div>
          </DraggableCard>
        );
      case 'right-field':
        return (
          <DraggableCard id={cardId} className="col-span-2">
            <FieldCard
              title="Empty field"
              area="15 ha"
              imageUrl={mapPlaceholder}
              alt="Empty field"
            />
          </DraggableCard>
        );
      case 'status-cards':
        return (
          <DraggableCard id={cardId} className="col-span-3">
            <div className="relative flex flex-col items-center min-h-[300px]">
              <Image
                src="https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=800&q=80"
                alt="Campo"
                fill
                className="object-cover blur-md absolute inset-0 z-0 rounded-3xl"
                style={{objectPosition: 'center'}}
              />
              <div className="absolute inset-0 bg-black bg-opacity-50 z-10 rounded-3xl" />
              <div className="relative z-20 flex w-full gap-4 p-6">
                <div className="flex-1 rounded-2xl bg-black/50 backdrop-blur-md shadow-xl p-6 flex flex-col items-center">
                  <span className="mb-1 block">
                    <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <g filter="url(#ph-blur)">
                        <ellipse cx="18" cy="28" rx="8" ry="3" fill="#22C55E" fillOpacity="0.15"/>
                      </g>
                      <path d="M18 28C18 28 10 22 10 15C10 10 14 6 18 6C22 6 26 10 26 15C26 22 18 28 18 28Z" stroke="#22C55E" strokeWidth="2" fill="none"/>
                      <path d="M18 28C18 28 14 22 14 17C14 14 16 12 18 12C20 12 22 14 22 17C22 22 18 28 18 28Z" stroke="#22C55E" strokeWidth="2" fill="none"/>
                      <defs>
                        <filter id="ph-blur" x="6" y="23" width="24" height="10" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
                          <feGaussianBlur stdDeviation="1.5"/>
                        </filter>
                      </defs>
                    </svg>
                  </span>
                  <span className="text-4xl font-extrabold text-white drop-shadow">93%</span>
                  <div className="text-white text-xs mb-1 drop-shadow">of reference value</div>
                  <div className="text-white font-semibold text-lg drop-shadow">Plant's health</div>
                </div>
                <div className="flex-1 rounded-2xl bg-black/50 backdrop-blur-md shadow-xl p-6 flex flex-col items-center">
                  <span className="mb-1 block">
                    <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <g filter="url(#wd-blur)">
                        <ellipse cx="18" cy="28" rx="8" ry="3" fill="#38BDF8" fillOpacity="0.15"/>
                      </g>
                      <path d="M18 7C18 7 26 17 26 23C26 27 22.4183 31 18 31C13.5817 31 10 27 10 23C10 17 18 7 18 7Z" stroke="#BAE6FD" strokeWidth="2" fill="#38BDF8"/>
                      <defs>
                        <filter id="wd-blur" x="6" y="23" width="24" height="10" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
                          <feGaussianBlur stdDeviation="1.5"/>
                        </filter>
                      </defs>
                    </svg>
                  </span>
                  <span className="text-4xl font-extrabold text-white drop-shadow">85%</span>
                  <div className="text-white text-xs mb-1 drop-shadow">of reference value</div>
                  <div className="text-white font-semibold text-lg drop-shadow">Water depth</div>
                </div>
                <div className="flex-1 rounded-2xl bg-black/50 backdrop-blur-md shadow-xl p-6 flex flex-col items-center">
                  <span className="mb-1 block">
                    <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <g filter="url(#soil-blur)">
                        <ellipse cx="18" cy="28" rx="8" ry="3" fill="#FACC15" fillOpacity="0.15"/>
                      </g>
                      <circle cx="18" cy="17" r="7" stroke="#FACC15" strokeWidth="2" fill="#FDE68A"/>
                      <path d="M11 17C11 17 13.5 14 18 14C22.5 14 25 17 25 17" stroke="#22C55E" strokeWidth="1.5" fill="none"/>
                      <path d="M18 10C18 10 18 24 18 24" stroke="#22C55E" strokeWidth="1.5" fill="none"/>
                      <defs>
                        <filter id="soil-blur" x="6" y="23" width="24" height="10" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
                          <feGaussianBlur stdDeviation="1.5"/>
                        </filter>
                      </defs>
                    </svg>
                  </span>
                  <span className="text-4xl font-extrabold text-white drop-shadow">74%</span>
                  <div className="text-white text-xs mb-1 drop-shadow">of reference value</div>
                  <div className="text-white font-semibold text-lg drop-shadow">Soil</div>
                </div>
              </div>
              <div className="relative z-20 flex justify-between w-full text-white text-base drop-shadow mt-4 px-6">
                <span>10 days to harvest</span>
                <span>64/74</span>
              </div>
              <div className="relative z-20 w-full flex items-center justify-center px-6 mt-2">
                <div className="w-full h-2 bg-white/20 rounded-full overflow-hidden shadow-inner">
                  <div
                    className="h-full bg-lime-400 rounded-full transition-all duration-500"
                    style={{ width: `${(64/74)*100}%` }}
                  />
                </div>
              </div>
              <div className="h-6" />
            </div>
          </DraggableCard>
        );
      case 'recommendations':
        return (
          <DraggableCard id={cardId} className="col-span-1">
            <div className="bg-white rounded-2xl shadow-lg p-4 flex flex-col gap-3 border border-gray-100 min-h-[300px] h-full">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                    <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="#3B82F6"/>
                  </svg>
                </div>
                <span className="text-sm font-semibold text-gray-800">Recommendations</span>
              </div>

              <div className="flex flex-col gap-3 flex-1">
                <div className="bg-blue-50 rounded-lg p-3 border-l-4 border-blue-400">
                  <div className="flex items-center gap-2 mb-1">
                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none">
                      <path d="M12 3C12 3 20 13 20 19C20 22 16.4183 25 12 25C7.5817 25 4 22 4 19C4 13 12 3 12 3Z" fill="#3B82F6"/>
                    </svg>
                    <span className="text-xs font-semibold text-blue-800">Irrigation</span>
                  </div>
                  <p className="text-xs text-blue-700 leading-tight">Increase watering by 15% due to low humidity (74%)</p>
                </div>

                <div className="bg-green-50 rounded-lg p-3 border-l-4 border-green-400">
                  <div className="flex items-center gap-2 mb-1">
                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none">
                      <path d="M12 2L12 22M7 7L12 2L17 7M7 17L12 22L17 17" stroke="#22C55E" strokeWidth="2" fill="none"/>
                    </svg>
                    <span className="text-xs font-semibold text-green-800">Fertilizer</span>
                  </div>
                  <p className="text-xs text-green-700 leading-tight">Apply phosphorus supplement (current: 10.5)</p>
                </div>

                <div className="bg-orange-50 rounded-lg p-3 border-l-4 border-orange-400">
                  <div className="flex items-center gap-2 mb-1">
                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none">
                      <path d="M12 9V13M12 17H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="#F97316" strokeWidth="2" fill="none"/>
                    </svg>
                    <span className="text-xs font-semibold text-orange-800">Weather</span>
                  </div>
                  <p className="text-xs text-orange-700 leading-tight">Rain expected in 2 days. Plan harvest accordingly</p>
                </div>

                <div className="bg-red-50 rounded-lg p-3 border-l-4 border-red-400">
                  <div className="flex items-center gap-2 mb-1">
                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none">
                      <circle cx="12" cy="12" r="3" fill="#EF4444"/>
                      <path d="M12 1V5M12 19V23M4.22 4.22L6.34 6.34M17.66 17.66L19.78 19.78M1 12H5M19 12H23M4.22 19.78L6.34 17.66M17.66 6.34L19.78 4.22" stroke="#EF4444" strokeWidth="2"/>
                    </svg>
                    <span className="text-xs font-semibold text-red-800">Pest Alert</span>
                  </div>
                  <p className="text-xs text-red-700 leading-tight">Monitor for corn borer activity in warm weather</p>
                </div>
              </div>

              <button className="bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium py-2 px-3 rounded-lg transition-colors mt-auto">
                View All
              </button>
            </div>
          </DraggableCard>
        );
      case 'map':
        return (
          <DraggableCard id={cardId} className="col-span-1">
            <div className="bg-white rounded-2xl shadow-lg p-4 flex flex-col gap-3 h-full">
              <div className="relative w-full h-32 mb-2">
                <Image src={mapPlaceholder} alt="Map" fill className="rounded-lg object-cover" />
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="bg-green-600 text-white text-xs px-2 py-1 rounded font-bold shadow text-center">
                    High<br/>85 kg/h<br/>4.5 ha
                  </div>
                </div>
                <div className="absolute top-2 right-2 w-3 h-3 bg-purple-500 rounded-full border-2 border-white"></div>
              </div>
              <div className="space-y-1">
                <div className="text-xs text-gray-500">
                  Low <span className="bg-gray-200 text-gray-700 rounded px-1 font-bold text-xs">115 kg/h</span> <span className="text-gray-400">6.2 ha</span>
                </div>
                <div className="text-xs text-gray-500">Phosphorus <b className="text-gray-700">10.5</b></div>
                <div className="text-xs text-gray-500">Magnesium <b className="text-gray-700">7.2</b></div>
                <div className="text-xs text-gray-500">Acidity <b className="text-gray-700">3.0</b></div>
                <div className="text-xs text-gray-500">Humidity <b className="text-gray-700">38%</b></div>
              </div>
              <button className="bg-white border border-gray-300 text-gray-700 rounded-full w-8 h-8 flex items-center justify-center self-end mt-2 hover:bg-gray-50 transition-colors">
                <ArrowForwardIcon fontSize="small" />
              </button>
            </div>
          </DraggableCard>
        );
      case 'crop-selector':
        return (
          <DraggableCard id={cardId} className="col-span-3">
            <div className="bg-white rounded-2xl shadow-lg p-4 flex items-center gap-4 border border-gray-100 h-full">
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-500">Crop</span>
                <select className="border rounded px-3 py-2 text-sm bg-lime-100 text-green-800 font-medium">
                  <option>Corn, grain</option>
                  <option>Wheat</option>
                  <option>Barley</option>
                </select>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-500">Standard rate</span>
                <input className="border rounded px-3 py-2 w-16 text-sm text-center" defaultValue="100" readOnly />
                <span className="text-sm bg-lime-300 text-green-800 rounded px-2 py-1 font-semibold">kg/ha</span>
                <span className="text-sm bg-gray-200 text-gray-600 rounded px-2 py-1">L/ha</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-500">Productivity zones</span>
                <div className="flex gap-1">
                  {[3,4,5,6,7].map((zone) => (
                    <span key={zone} className={`rounded px-2 py-1 text-sm font-bold ${zone === 4 ? 'bg-lime-300 text-green-900' : 'bg-gray-200 text-gray-600'}`}>
                      {zone}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </DraggableCard>
        );
      case 'growth-chart':
        return (
          <DraggableCard id={cardId} className="col-span-2">
            <div className="bg-white rounded-2xl shadow-lg p-4 flex flex-col border border-gray-100 h-full">
              <div className="flex items-center justify-between mb-3">
                <span className="text-sm text-gray-500">Growth rate</span>
                <div className="flex items-center gap-1 text-xs text-gray-400">
                  <span>W</span>
                  <span>M</span>
                  <span>Y</span>
                </div>
              </div>
              <div className="flex items-center gap-2 mb-3">
                <span className="text-2xl font-extrabold">0.75</span>
                <span className="text-green-500 text-lg">↓</span>
              </div>
              <div className="w-full h-16 bg-gray-50 rounded flex items-end gap-1 px-2 py-2">
                {[4,8,6,10,7,12,8,6,9,5,7,8,10,6,8,7].map((v,i) => (
                  <div key={i} className="bg-lime-400 rounded-sm flex-1" style={{height: `${v*4}px`}}></div>
                ))}
              </div>
            </div>
          </DraggableCard>
        );
      default:
        return null;
    }
  };

  return (
    <main className="min-h-screen bg-gray-200 relative overflow-hidden font-sans">
      {/* Background */}
      <div className="absolute inset-0 -z-10 bg-gradient-to-br from-gray-100 to-gray-300" />

      {/* Settings Button */}
      <button
        onClick={() => setIsSettingsOpen(true)}
        className="fixed top-4 right-4 z-40 bg-blue-600 hover:bg-blue-700 text-white rounded-full p-3 shadow-lg transition-colors"
        title="Dashboard Settings"
      >
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
          <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" stroke="currentColor" strokeWidth="2"/>
          <path d="M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.2579 9.77251 19.9887C9.5799 19.7194 9.31074 19.5143 9 19.4C8.69838 19.2669 8.36381 19.2272 8.03941 19.286C7.71502 19.3448 7.41568 19.4995 7.18 19.73L7.12 19.79C6.93425 19.976 6.71368 20.1235 6.47088 20.2241C6.22808 20.3248 5.96783 20.3766 5.705 20.3766C5.44217 20.3766 5.18192 20.3248 4.93912 20.2241C4.69632 20.1235 4.47575 19.976 4.29 19.79C4.10405 19.6043 3.95653 19.3837 3.85588 19.1409C3.75523 18.8981 3.70343 18.6378 3.70343 18.375C3.70343 18.1122 3.75523 17.8519 3.85588 17.6091C3.95653 17.3663 4.10405 17.1457 4.29 16.96L4.35 16.9C4.58054 16.6643 4.73519 16.365 4.794 16.0406C4.85282 15.7162 4.81312 15.3816 4.68 15.08C4.55324 14.7842 4.34276 14.532 4.07447 14.3543C3.80618 14.1766 3.49179 14.0813 3.17 14.08H3C2.46957 14.08 1.96086 13.8693 1.58579 13.4942C1.21071 13.1191 1 12.6104 1 12.08C1 11.5496 1.21071 11.0409 1.58579 10.6658C1.96086 10.2907 2.46957 10.08 3 10.08H3.09C3.42099 10.0723 3.742 9.96512 4.01127 9.77251C4.28054 9.5799 4.48571 9.31074 4.6 9C4.73312 8.69838 4.77282 8.36381 4.714 8.03941C4.65519 7.71502 4.50054 7.41568 4.27 7.18L4.21 7.12C4.02405 6.93425 3.87653 6.71368 3.77588 6.47088C3.67523 6.22808 3.62343 5.96783 3.62343 5.705C3.62343 5.44217 3.67523 5.18192 3.77588 4.93912C3.87653 4.69632 4.02405 4.47575 4.21 4.29C4.39575 4.10405 4.61632 3.95653 4.85912 3.85588C5.10192 3.75523 5.36217 3.70343 5.625 3.70343C5.88783 3.70343 6.14808 3.75523 6.39088 3.85588C6.63368 3.95653 6.85425 4.10405 7.04 4.29L7.1 4.35C7.33568 4.58054 7.63502 4.73519 7.95941 4.794C8.28381 4.85282 8.61838 4.81312 8.92 4.68H9C9.29577 4.55324 9.54802 4.34276 9.72569 4.07447C9.90337 3.80618 9.99872 3.49179 10 3.17V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z" stroke="currentColor" strokeWidth="2"/>
        </svg>
      </button>

      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
      >
        <div className="max-w-7xl mx-auto p-6 grid gap-4">
          {/* Top Section */}
          <SortableContext items={visibleCards} strategy={rectSortingStrategy}>
            <div className="grid grid-cols-12 gap-4">
              {visibleCards.slice(0, 4).map(cardId => renderCard(cardId))}
            </div>
          </SortableContext>

          {/* Main Section */}
          <div className="grid grid-cols-5 gap-4 items-start">
            {/* Status Cards Block */}
            {visibleCards.includes('status-cards') && (
              <DraggableCard id="status-cards" className="col-span-3">
                <div className="relative flex flex-col items-center min-h-[300px]">
                  <Image
                    src="https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=800&q=80"
                    alt="Campo"
                    fill
                    className="object-cover blur-md absolute inset-0 z-0 rounded-3xl"
                    style={{objectPosition: 'center'}}
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-50 z-10 rounded-3xl" />
                  <div className="relative z-20 flex w-full gap-4 p-6">
                    <div className="flex-1 rounded-2xl bg-black/50 backdrop-blur-md shadow-xl p-6 flex flex-col items-center">
                      <span className="mb-1 block">
                        <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <g filter="url(#ph-blur)">
                            <ellipse cx="18" cy="28" rx="8" ry="3" fill="#22C55E" fillOpacity="0.15"/>
                          </g>
                          <path d="M18 28C18 28 10 22 10 15C10 10 14 6 18 6C22 6 26 10 26 15C26 22 18 28 18 28Z" stroke="#22C55E" strokeWidth="2" fill="none"/>
                          <path d="M18 28C18 28 14 22 14 17C14 14 16 12 18 12C20 12 22 14 22 17C22 22 18 28 18 28Z" stroke="#22C55E" strokeWidth="2" fill="none"/>
                          <defs>
                            <filter id="ph-blur" x="6" y="23" width="24" height="10" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
                              <feGaussianBlur stdDeviation="1.5"/>
                            </filter>
                          </defs>
                        </svg>
                      </span>
                      <span className="text-4xl font-extrabold text-white drop-shadow">93%</span>
                      <div className="text-white text-xs mb-1 drop-shadow">of reference value</div>
                      <div className="text-white font-semibold text-lg drop-shadow">Plant's health</div>
                    </div>
                    <div className="flex-1 rounded-2xl bg-black/50 backdrop-blur-md shadow-xl p-6 flex flex-col items-center">
                      <span className="mb-1 block">
                        <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <g filter="url(#wd-blur)">
                            <ellipse cx="18" cy="28" rx="8" ry="3" fill="#38BDF8" fillOpacity="0.15"/>
                          </g>
                          <path d="M18 7C18 7 26 17 26 23C26 27 22.4183 31 18 31C13.5817 31 10 27 10 23C10 17 18 7 18 7Z" stroke="#BAE6FD" strokeWidth="2" fill="#38BDF8"/>
                          <defs>
                            <filter id="wd-blur" x="6" y="23" width="24" height="10" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
                              <feGaussianBlur stdDeviation="1.5"/>
                            </filter>
                          </defs>
                        </svg>
                      </span>
                      <span className="text-4xl font-extrabold text-white drop-shadow">85%</span>
                      <div className="text-white text-xs mb-1 drop-shadow">of reference value</div>
                      <div className="text-white font-semibold text-lg drop-shadow">Water depth</div>
                    </div>
                    <div className="flex-1 rounded-2xl bg-black/50 backdrop-blur-md shadow-xl p-6 flex flex-col items-center">
                      <span className="mb-1 block">
                        <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <g filter="url(#soil-blur)">
                            <ellipse cx="18" cy="28" rx="8" ry="3" fill="#FACC15" fillOpacity="0.15"/>
                          </g>
                          <circle cx="18" cy="17" r="7" stroke="#FACC15" strokeWidth="2" fill="#FDE68A"/>
                          <path d="M11 17C11 17 13.5 14 18 14C22.5 14 25 17 25 17" stroke="#22C55E" strokeWidth="1.5" fill="none"/>
                          <path d="M18 10C18 10 18 24 18 24" stroke="#22C55E" strokeWidth="1.5" fill="none"/>
                          <defs>
                            <filter id="soil-blur" x="6" y="23" width="24" height="10" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
                              <feGaussianBlur stdDeviation="1.5"/>
                            </filter>
                          </defs>
                        </svg>
                      </span>
                      <span className="text-4xl font-extrabold text-white drop-shadow">74%</span>
                      <div className="text-white text-xs mb-1 drop-shadow">of reference value</div>
                      <div className="text-white font-semibold text-lg drop-shadow">Soil</div>
                    </div>
                  </div>
                  <div className="relative z-20 flex justify-between w-full text-white text-base drop-shadow mt-4 px-6">
                    <span>10 days to harvest</span>
                    <span>64/74</span>
                  </div>
                  <div className="relative z-20 w-full flex items-center justify-center px-6 mt-2">
                    <div className="w-full h-2 bg-white/20 rounded-full overflow-hidden shadow-inner">
                      <div
                        className="h-full bg-lime-400 rounded-full transition-all duration-500"
                        style={{ width: `${(64/74)*100}%` }}
                      />
                    </div>
                  </div>
                  <div className="h-6" />
                </div>
              </DraggableCard>
            )}
            {/* Recommendations Card */}
            {visibleCards.includes('recommendations') && (
              <DraggableCard id="recommendations" className="col-span-1">
                <div className="bg-white rounded-2xl shadow-lg p-4 flex flex-col gap-3 border border-gray-100 min-h-[300px] h-full">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                      <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                        <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="#3B82F6"/>
                      </svg>
                    </div>
                    <span className="text-sm font-semibold text-gray-800">Recommendations</span>
                  </div>

                  <div className="flex flex-col gap-3 flex-1">
                    <div className="bg-blue-50 rounded-lg p-3 border-l-4 border-blue-400">
                      <div className="flex items-center gap-2 mb-1">
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none">
                          <path d="M12 3C12 3 20 13 20 19C20 22 16.4183 25 12 25C7.5817 25 4 22 4 19C4 13 12 3 12 3Z" fill="#3B82F6"/>
                        </svg>
                        <span className="text-xs font-semibold text-blue-800">Irrigation</span>
                      </div>
                      <p className="text-xs text-blue-700 leading-tight">Increase watering by 15% due to low humidity (74%)</p>
                    </div>

                    <div className="bg-green-50 rounded-lg p-3 border-l-4 border-green-400">
                      <div className="flex items-center gap-2 mb-1">
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none">
                          <path d="M12 2L12 22M7 7L12 2L17 7M7 17L12 22L17 17" stroke="#22C55E" strokeWidth="2" fill="none"/>
                        </svg>
                        <span className="text-xs font-semibold text-green-800">Fertilizer</span>
                      </div>
                      <p className="text-xs text-green-700 leading-tight">Apply phosphorus supplement (current: 10.5)</p>
                    </div>

                    <div className="bg-orange-50 rounded-lg p-3 border-l-4 border-orange-400">
                      <div className="flex items-center gap-2 mb-1">
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none">
                          <path d="M12 9V13M12 17H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="#F97316" strokeWidth="2" fill="none"/>
                        </svg>
                        <span className="text-xs font-semibold text-orange-800">Weather</span>
                      </div>
                      <p className="text-xs text-orange-700 leading-tight">Rain expected in 2 days. Plan harvest accordingly</p>
                    </div>

                    <div className="bg-red-50 rounded-lg p-3 border-l-4 border-red-400">
                      <div className="flex items-center gap-2 mb-1">
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none">
                          <circle cx="12" cy="12" r="3" fill="#EF4444"/>
                          <path d="M12 1V5M12 19V23M4.22 4.22L6.34 6.34M17.66 17.66L19.78 19.78M1 12H5M19 12H23M4.22 19.78L6.34 17.66M17.66 6.34L19.78 4.22" stroke="#EF4444" strokeWidth="2"/>
                        </svg>
                        <span className="text-xs font-semibold text-red-800">Pest Alert</span>
                      </div>
                      <p className="text-xs text-red-700 leading-tight">Monitor for corn borer activity in warm weather</p>
                    </div>
                  </div>

                  <button className="bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium py-2 px-3 rounded-lg transition-colors mt-auto">
                    View All
                  </button>
                </div>
              </DraggableCard>
            )}

            {/* Map Card */}
            {visibleCards.includes('map') && (
              <DraggableCard id="map" className="col-span-1">
                <div className="bg-white rounded-2xl shadow-lg p-4 flex flex-col gap-3 h-full">
                  <div className="relative w-full h-32 mb-2">
                    <Image src={mapPlaceholder} alt="Map" fill className="rounded-lg object-cover" />
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="bg-green-600 text-white text-xs px-2 py-1 rounded font-bold shadow text-center">
                        High<br/>85 kg/h<br/>4.5 ha
                      </div>
                    </div>
                    <div className="absolute top-2 right-2 w-3 h-3 bg-purple-500 rounded-full border-2 border-white"></div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-xs text-gray-500">
                      Low <span className="bg-gray-200 text-gray-700 rounded px-1 font-bold text-xs">115 kg/h</span> <span className="text-gray-400">6.2 ha</span>
                    </div>
                    <div className="text-xs text-gray-500">Phosphorus <b className="text-gray-700">10.5</b></div>
                    <div className="text-xs text-gray-500">Magnesium <b className="text-gray-700">7.2</b></div>
                    <div className="text-xs text-gray-500">Acidity <b className="text-gray-700">3.0</b></div>
                    <div className="text-xs text-gray-500">Humidity <b className="text-gray-700">38%</b></div>
                  </div>
                  <button className="bg-white border border-gray-300 text-gray-700 rounded-full w-8 h-8 flex items-center justify-center self-end mt-2 hover:bg-gray-50 transition-colors">
                    <ArrowForwardIcon fontSize="small" />
                  </button>
                </div>
              </DraggableCard>
            )}
          </div>
          {/* Bottom Section */}
          <div className="grid grid-cols-5 gap-4 items-center">
            {/* Crop Selector */}
            {visibleCards.includes('crop-selector') && (
              <DraggableCard id="crop-selector" className="col-span-3">
                <div className="bg-white rounded-2xl shadow-lg p-4 flex items-center gap-4 border border-gray-100 h-full">
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-500">Crop</span>
                    <select className="border rounded px-3 py-2 text-sm bg-lime-100 text-green-800 font-medium">
                      <option>Corn, grain</option>
                      <option>Wheat</option>
                      <option>Barley</option>
                    </select>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-500">Standard rate</span>
                    <input className="border rounded px-3 py-2 w-16 text-sm text-center" defaultValue="100" readOnly />
                    <span className="text-sm bg-lime-300 text-green-800 rounded px-2 py-1 font-semibold">kg/ha</span>
                    <span className="text-sm bg-gray-200 text-gray-600 rounded px-2 py-1">L/ha</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-500">Productivity zones</span>
                    <div className="flex gap-1">
                      {[3,4,5,6,7].map((zone) => (
                        <span key={zone} className={`rounded px-2 py-1 text-sm font-bold ${zone === 4 ? 'bg-lime-300 text-green-900' : 'bg-gray-200 text-gray-600'}`}>
                          {zone}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </DraggableCard>
            )}

            {/* Growth Rate Chart */}
            {visibleCards.includes('growth-chart') && (
              <DraggableCard id="growth-chart" className="col-span-2">
                <div className="bg-white rounded-2xl shadow-lg p-4 flex flex-col border border-gray-100 h-full">
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-sm text-gray-500">Growth rate</span>
                    <div className="flex items-center gap-1 text-xs text-gray-400">
                      <span>W</span>
                      <span>M</span>
                      <span>Y</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 mb-3">
                    <span className="text-2xl font-extrabold">0.75</span>
                    <span className="text-green-500 text-lg">↓</span>
                  </div>
                  <div className="w-full h-16 bg-gray-50 rounded flex items-end gap-1 px-2 py-2">
                    {[4,8,6,10,7,12,8,6,9,5,7,8,10,6,8,7].map((v,i) => (
                      <div key={i} className="bg-lime-400 rounded-sm flex-1" style={{height: `${v*4}px`}}></div>
                    ))}
                  </div>
                </div>
              </DraggableCard>
            )}
          </div>
        </div>
      </DndContext>

      {/* Settings Modal */}
      <DashboardSettings
        isOpen={isSettingsOpen}
        onClose={() => setIsSettingsOpen(false)}
      />
    </main>
  );
}
