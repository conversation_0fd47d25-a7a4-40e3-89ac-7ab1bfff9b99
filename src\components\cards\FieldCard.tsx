'use client';

import React from 'react';
import Image from "next/image";

interface FieldCardProps {
  title: string;
  area: string;
  imageUrl: string;
  alt: string;
}

export const FieldCard: React.FC<FieldCardProps> = ({
  title,
  area,
  imageUrl,
  alt
}) => {
  return (
    <div className="bg-white rounded-2xl shadow-lg p-4 flex flex-col border border-gray-100 h-full">
      <div className="w-full h-24 mb-1 relative overflow-hidden rounded-xl">
        <Image src={imageUrl} alt={alt} fill className="object-cover" />
        <svg className="absolute inset-0 w-full h-full" viewBox="0 0 112 80" fill="none">
          <polygon points="22,58 38,28 85,22 95,48 68,68" fill="#D9F99D" fillOpacity="0.5" stroke="#A3E635" strokeWidth="2" />
        </svg>
      </div>
      <div className="flex items-center justify-between w-full mt-1 gap-2">
        <div className="flex flex-col">
          <div className="text-base font-bold leading-tight">{title}</div>
          <div className="text-xs text-gray-400 leading-tight">{area}</div>
        </div>
        <button className="bg-lime-300 hover:bg-lime-400 text-green-900 rounded-full w-8 h-8 flex items-center justify-center shadow transition-all duration-200">
          <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
            <path d="M8 12H16" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
            <path d="M13 9L16 12L13 15" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
          </svg>
        </button>
      </div>
    </div>
  );
};
