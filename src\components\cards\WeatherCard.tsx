'use client';

import React from 'react';
import LocationOnIcon from '@mui/icons-material/LocationOn';

export const WeatherCard: React.FC = () => {
  return (
    <div className="bg-white rounded-2xl shadow-lg p-4 flex flex-col justify-between border border-gray-100 h-full">
      <div className="flex items-center gap-1 text-gray-700 font-medium mb-2">
        <LocationOnIcon fontSize="small" className="text-gray-500" />
        <span className="text-xs">Vynnyky, Lviv Oblast, Ukraine</span>
      </div>
      <div className="flex items-center gap-2 mb-3">
        <div className="text-4xl font-extrabold text-gray-800">+16°</div>
        <div className="flex flex-col text-xs text-gray-500">
          <span>H: +19°</span>
          <span>L: +10°</span>
        </div>
        <div className="ml-auto">
          <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
            <svg width="24" height="24" viewBox="0 0 32 32" fill="none">
              <path d="M24 18C26.2091 18 28 16.2091 28 14C28 11.7909 26.2091 10 24 10C23.7 10 23.4 10.1 23.1 10.1C22.5 7.6 20.4 6 18 6C15.2 6 13 8.2 13 11C13 11.3 13 11.6 13.1 11.9C11.3 12.4 10 14 10 16C10 18.2 11.8 20 14 20H24Z" fill="#9CA3AF"/>
            </svg>
          </div>
        </div>
      </div>
      <div className="grid grid-cols-2 gap-1 text-xs text-gray-500">
        <div className="text-center">
          <div className="font-medium text-gray-700">Humidity</div>
          <div className="font-bold text-gray-800">74%</div>
        </div>
        <div className="text-center">
          <div className="font-medium text-gray-700">Precipitation</div>
          <div className="font-bold text-gray-800">5 mm</div>
        </div>
        <div className="text-center">
          <div className="font-medium text-gray-700">Pressure</div>
          <div className="font-bold text-gray-800">1019 hPa</div>
        </div>
        <div className="text-center">
          <div className="font-medium text-gray-700">Wind</div>
          <div className="font-bold text-gray-800">18 km/h</div>
        </div>
      </div>
    </div>
  );
};
